#!/usr/bin/env python3
"""
Neural G1 - CSV File Normalizer
Normalizes XAUUSD CSV files to a consistent format for Google Colab training

This script:
1. Reads your original CSV files (tab-separated with various formats)
2. Normalizes them to a standard format: DateTime,Open,High,Low,Close,Volume
3. Saves normalized files ready for Google Colab upload

Author: Neural G1 Development Team
Date: 2025-06-14
"""

import pandas as pd
import os
from datetime import datetime
import glob

def normalize_csv_file(input_path, output_path, timeframe):
    """
    Normalize a single CSV file to standard format
    
    Args:
        input_path (str): Path to original CSV file
        output_path (str): Path for normalized CSV file
        timeframe (str): Timeframe identifier (M1, M5, etc.)
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        print(f"📂 Processing {timeframe}: {input_path}")
        
        # Read the Gold_data CSV file with semicolon separator
        # Gold_data format: Date;Open;High;Low;Close;Volume
        print(f"   🔍 Reading Gold_data file with semicolon separator...")

        # Read with semicolon separator and proper headers
        df = pd.read_csv(input_path, sep=';', encoding='utf-8')

        print(f"   📊 Raw data shape: {df.shape}")
        print(f"   📊 Raw data columns: {list(df.columns)}")

        # Gold_data files have columns: Date, Open, High, Low, Close, Volume
        expected_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
        if list(df.columns) != expected_columns:
            print(f"   ⚠️ Warning: Expected columns {expected_columns}, got {list(df.columns)}")
            # Try to map columns if they're similar
            if len(df.columns) >= 6:
                df.columns = expected_columns[:len(df.columns)]
                if len(df.columns) > 6:
                    df = df[expected_columns]  # Keep only the first 6 columns
            else:
                raise ValueError(f"Unexpected number of data columns: {len(df.columns)}")

        # Rename Date column to Time for consistency with existing logic
        df = df.rename(columns={'Date': 'Time'})

        print(f"   📊 Final shape: {df.shape}")
        print(f"   📊 Final columns: {list(df.columns)}")

        # Debug: Check first few rows to see what we actually have
        print(f"   🔍 First 3 rows of data:")
        for i in range(min(3, len(df))):
            print(f"      Row {i+1}: {df.iloc[i].tolist()}")
        
        # Convert Time to proper DateTime
        print(f"   📅 Sample Time values: {df['Time'].head(3).tolist()}")
        print(f"   📅 Time column data type: {df['Time'].dtype}")

        # Your Time column should already be datetime strings
        if df['Time'].dtype == 'object':  # String type
            df['DateTime'] = pd.to_datetime(df['Time'], errors='coerce')
        else:
            # If it's numeric, something went wrong with column selection
            print(f"   ❌ ERROR: Time column is numeric ({df['Time'].dtype}), not datetime strings!")
            print(f"   🔍 This means the columns were misaligned during reading")
            raise ValueError("Time column contains numeric data instead of datetime strings")
        
        # Remove rows with invalid datetime
        initial_rows = len(df)
        df = df.dropna(subset=['DateTime'])
        if len(df) != initial_rows:
            print(f"   🧹 Removed {initial_rows - len(df)} rows with invalid datetime")
        
        # Sort by datetime
        df = df.sort_values('DateTime')
        
        # Process OHLCV columns
        ohlcv_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
        for col in ohlcv_cols:
            if col in df.columns:
                # Convert to numeric
                df[col] = pd.to_numeric(df[col], errors='coerce')
                
                # Check for NaN values
                nan_count = df[col].isna().sum()
                if nan_count > 0:
                    print(f"   ⚠️ Warning: {nan_count} NaN values in {col}")
        
        # Remove rows with NaN in OHLCV
        initial_rows = len(df)
        df = df.dropna(subset=ohlcv_cols)
        if len(df) != initial_rows:
            print(f"   🧹 Removed {initial_rows - len(df)} rows with NaN values")
        
        if len(df) == 0:
            raise ValueError("No valid data after processing")
        
        # Create final normalized DataFrame
        normalized_df = pd.DataFrame({
            'DateTime': df['DateTime'],
            'Open': df['Open'],
            'High': df['High'],
            'Low': df['Low'],
            'Close': df['Close'],
            'Volume': df['Volume']
        })
        
        # Remove duplicates
        initial_rows = len(normalized_df)
        normalized_df = normalized_df.drop_duplicates(subset=['DateTime'])
        if len(normalized_df) != initial_rows:
            print(f"   🧹 Removed {initial_rows - len(normalized_df)} duplicate rows")
        
        # Sort by DateTime
        normalized_df = normalized_df.sort_values('DateTime')
        
        # Validate data integrity
        if normalized_df['High'].min() < normalized_df['Low'].max():
            print("   ⚠️ Warning: Some High values are lower than Low values")
        
        # Save normalized file
        normalized_df.to_csv(output_path, index=False, encoding='utf-8')
        
        # Print summary
        date_range = f"{normalized_df['DateTime'].min().strftime('%Y-%m-%d')} to {normalized_df['DateTime'].max().strftime('%Y-%m-%d')}"
        price_range = f"${normalized_df['Close'].min():.2f} - ${normalized_df['Close'].max():.2f}"
        
        print(f"   ✅ Normalized {timeframe}:")
        print(f"      📊 Rows: {len(normalized_df):,}")
        print(f"      📅 Period: {date_range}")
        print(f"      💰 Price range: {price_range}")
        print(f"      💾 Saved to: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error processing {timeframe}: {str(e)}")
        return False

def main():
    """
    Main function to normalize all XAUUSD CSV files from Gold_data directory
    """
    print("🧠 Neural G1 - CSV File Normalizer (Updated for Gold_data)")
    print("=" * 60)

    # Define timeframe mapping: Neural G1 format -> Gold_data filename
    timeframe_mapping = {
        'M1': 'XAU_1m_data.csv',
        'M5': 'XAU_5m_data.csv',
        'M15': 'XAU_15m_data.csv',
        'M30': 'XAU_30m_data.csv',
        'H1': 'XAU_1h_data.csv',
        'H4': 'XAU_4h_data.csv',
        'D1': 'XAU_1d_data.csv'
        # Note: XAU_1w_data.csv and XAU_1Month_data.csv are available but not used in Neural G1
    }

    # Input and output directories
    input_dir = "Gold_data"
    output_dir = "normalized_data"

    # Check if Gold_data directory exists
    if not os.path.exists(input_dir):
        print(f"❌ Error: {input_dir} directory not found!")
        print(f"   Please ensure the Gold_data directory exists with the CSV files.")
        return

    # Create output directory
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 Created output directory: {output_dir}")

    print(f"📂 Input directory: {input_dir}/")
    print(f"📁 Output directory: {output_dir}/")
    print()

    # Process each timeframe
    success_count = 0
    total_count = 0

    for neural_tf, gold_filename in timeframe_mapping.items():
        input_file = os.path.join(input_dir, gold_filename)
        output_file = os.path.join(output_dir, f"XAUUSD_{neural_tf}_normalized.csv")

        print(f"🔄 Processing {neural_tf}: {gold_filename} -> XAUUSD_{neural_tf}_normalized.csv")

        if os.path.exists(input_file):
            total_count += 1
            if normalize_csv_file(input_file, output_file, neural_tf):
                success_count += 1
            print()  # Empty line for readability
        else:
            print(f"❌ File not found: {input_file}")
            print(f"   Available files in {input_dir}:")
            if os.path.exists(input_dir):
                for file in os.listdir(input_dir):
                    if file.endswith('.csv'):
                        print(f"     - {file}")
            print()

    # Summary
    print("=" * 60)
    print(f"📊 Normalization Summary:")
    print(f"   ✅ Successfully processed: {success_count}/{total_count} files")
    print(f"   📁 Normalized files saved in: {output_dir}/")
    print()
    print("🚀 Next steps:")
    print("   1. Upload the normalized files from 'normalized_data/' folder to Google Drive")
    print("   2. Path: /content/drive/MyDrive/Neural_G1/normalized_data/")
    print("   3. Run the Neural G1 training notebook with the new dataset!")
    print("   4. The notebook will automatically detect and use the normalized files")

if __name__ == "__main__":
    main()
